export interface SCORE_LIST_INTERFACE {
    originScore: number;
    reportScore: number;
}
export interface COLUMNS_INTERFACE {
    label: string;
    field: string;
    width?: number;
    align: string;
    fixed?: string;
    children?: {
        label: string | number;
        field: string | number;
        width: number;
        align: string;
    }[];
}
export interface TABLE_OPTION_INTERFACE {
    [key: number]: string;
    originScoreSet: Set<any>;
    scoreList: SCORE_LIST_INTERFACE[];
}

export interface CHARACTER_COLUMNS_INTERFACE {
    label: string;
    field: string | number;
    align: string;
}
export interface CHARACTER_SCORE_LIST_INTERFACE {
    code: number;
    value?: number;
}
export interface CHARACTER_SCORE_TABLE_OPTION_INTERFACE {
    [key: number]: string;
    scoreList: CHARACTER_SCORE_LIST_INTERFACE[];
}
export interface CHARACTER_SCORE_QUERAY_DATA {
    paramA: number | undefined;
    paramB: number | undefined;
    scoreMin: number | undefined;
    scoreMax: number | undefined;
}
export interface CHARACTER_SCORE_INIT_FORM_DATA {
    [key: string]: number | undefined;
}
// hots类型定义
export interface HOTS_FORM_DATA extends CHARACTER_SCORE_QUERAY_DATA {}

// 五维测评2.0数据结构定义
export interface RowDataItem {
    encId?: string;
    encDimensionId: string;
    dimensionName: string;
    define?: any; // 新增字段，后端返回但可能为null
    weight: number;
    normalAverageScore: number;
    normalStandardDeviation: number;
}

export interface QualityConfigItem {
    encId?: string;
    headName: string;
    rowDataList: RowDataItem[];
}

export interface Character2ParamC {
    scoreMin?: number | null; // 改为可选，后端可能返回null
    scoreMax?: number | null; // 改为可选，后端可能返回null
    levelArray?: number[] | null; // 改为可选，后端可能返回null
    modelName?: string | null; // 新增字段
    keyPotentialQualityList: QualityConfigItem[];
    positionQualityModelMatchList: QualityConfigItem[];
    teamRoleList?: QualityConfigItem[] | null; // 改为可选，后端可能返回null
}

export interface Character2DetailData {
    fileParam?: any;
    productId?: number;
    productName?: string;
    paramA: number;
    paramB: number;
    paramC: Character2ParamC;
    updateTime?: string;
    createTime?: string;
    updateUserName?: string;
}
