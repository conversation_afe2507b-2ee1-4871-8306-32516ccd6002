<template>
    <div class="key-potential-quality-config">
        <div class="sub-title">
            <h4>关键潜在素质分数计算</h4>
        </div>

        <!-- 关键潜在素质分数权重表 -->
        <div class="section-title">
            <h5>1、关键潜在素质分数权重表</h5>
            <b-button v-if="configGroups.length < 16" type="outline" size="small" @click="addConfigGroup">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                添加一列
            </b-button>
        </div>

        <!-- 配置组管理 -->
        <div class="config-groups-header">
            <div v-for="(group, index) in configGroups" :key="group.id" class="config-group-item">
                <span class="group-label">配置{{ index + 1 }}：</span>
                <b-input v-model="group.headName" :placeholder="`请输入配置${index + 1}名称`" class="group-name-input" />
                <b-button v-if="configGroups.length > 1" type="outline" size="small" @click="removeConfigGroup(index)"> 删除 </b-button>
            </div>
        </div>

        <!-- 权重表格 -->
        <div class="weight-table-container">
            <table class="custom-table">
                <thead>
                    <tr>
                        <th class="dimension-header">二级维度</th>
                        <th v-for="(group, index) in configGroups" :key="group.id" class="config-header">
                            {{ group.headName || `配置${index + 1}` }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="dimension in dimensions" :key="dimension.id">
                        <td class="dimension-cell">{{ dimension.name }}</td>
                        <td v-for="(group, groupIndex) in configGroups" :key="`${dimension.id}_${group.id}`" class="weight-cell">
                            <b-input-number
                                v-model="weightMatrix[dimension.id][groupIndex]"
                                placeholder="权重"
                                hideButton
                                :min="0"
                                :max="1"
                                :precision="4"
                                @change="validateWeightSum(groupIndex)"
                            />
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- 权重和验证错误提示 -->
            <div v-for="(error, index) in weightSumErrors" :key="index" class="error-tip">
                {{ error }}
            </div>
        </div>

        <!-- 关键潜在素质常模 -->
        <div class="section-title">
            <h5>2、关键潜在素质常模</h5>
        </div>

        <!-- 常模表格 -->
        <div class="norm-table-container">
            <table class="custom-table">
                <thead>
                    <tr>
                        <th class="dimension-header">二级维度</th>
                        <th v-for="(group, index) in configGroups" :key="group.id" class="config-header">
                            <div class="norm-header">
                                <div class="norm-header-title">{{ group.headName || `配置${index + 1}` }}</div>
                                <div class="norm-header-labels">
                                    <span>常模平均分</span>
                                    <span>常模标准差</span>
                                </div>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="dimension in dimensions" :key="dimension.id">
                        <td class="dimension-cell">{{ dimension.name }}</td>
                        <td v-for="(group, groupIndex) in configGroups" :key="`${dimension.id}_${group.id}`" class="norm-cell">
                            <div class="norm-inputs">
                                <b-input-number
                                    v-model="avgScoreMatrix[dimension.id][groupIndex]"
                                    placeholder="常模平均分"
                                    hideButton
                                    :min="0"
                                    :max="1000"
                                    :precision="4"
                                    class="norm-input"
                                />
                                <b-input-number
                                    v-model="stdDevMatrix[dimension.id][groupIndex]"
                                    placeholder="常模标准差"
                                    hideButton
                                    :min="0.0001"
                                    :max="1000"
                                    :precision="4"
                                    class="norm-input"
                                />
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface ConfigGroup {
    id: string;
    headName: string;
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

// 配置组数据
const configGroups = ref<ConfigGroup[]>([{ id: '1', headName: '' }]);

// 维度数据
const dimensions = ref<{ id: string; name: string }[]>([]);

// 权重矩阵 - 维度ID -> 配置组索引 -> 权重值
const weightMatrix = ref<Record<string, (number | undefined)[]>>({});

// 常模矩阵
const avgScoreMatrix = ref<Record<string, (number | undefined)[]>>({});
const stdDevMatrix = ref<Record<string, (number | undefined)[]>>({});

// 权重和验证错误
const weightSumErrors = ref<string[]>([]);

// 验证指定配置组的权重和
function validateWeightSum(groupIndex: number) {
    const sum = dimensions.value.reduce((acc, dimension) => {
        return acc + (weightMatrix.value[dimension.id]?.[groupIndex] || 0);
    }, 0);

    const groupName = configGroups.value[groupIndex]?.headName || `配置${groupIndex + 1}`;

    if (Math.abs(sum - 1) > 0.0001) {
        weightSumErrors.value[groupIndex] = `${groupName} 权重和必须等于 1，当前为 ${sum.toFixed(4)}`;
    } else {
        weightSumErrors.value[groupIndex] = '';
    }

    // 清理空的错误信息
    weightSumErrors.value = weightSumErrors.value.filter((error) => error !== '');
}

// 添加配置组
function addConfigGroup() {
    if (configGroups.value.length >= 16) return;

    const newId = Date.now().toString();
    configGroups.value.push({
        id: newId,
        headName: '',
    });

    // 为所有维度添加新的权重和常模数据
    dimensions.value.forEach((dimension) => {
        if (!weightMatrix.value[dimension.id]) {
            weightMatrix.value[dimension.id] = [];
        }
        if (!avgScoreMatrix.value[dimension.id]) {
            avgScoreMatrix.value[dimension.id] = [];
        }
        if (!stdDevMatrix.value[dimension.id]) {
            stdDevMatrix.value[dimension.id] = [];
        }

        weightMatrix.value[dimension.id].push(undefined);
        avgScoreMatrix.value[dimension.id].push(undefined);
        stdDevMatrix.value[dimension.id].push(undefined);
    });
}

// 删除配置组
function removeConfigGroup(index: number) {
    if (configGroups.value.length <= 1) return;

    configGroups.value.splice(index, 1);

    // 从所有维度中删除对应的权重和常模数据
    dimensions.value.forEach((dimension) => {
        weightMatrix.value[dimension.id]?.splice(index, 1);
        avgScoreMatrix.value[dimension.id]?.splice(index, 1);
        stdDevMatrix.value[dimension.id]?.splice(index, 1);
    });

    // 重新验证所有配置组的权重和
    configGroups.value.forEach((_, groupIndex) => {
        validateWeightSum(groupIndex);
    });
}

// 初始化矩阵数据
function initializeMatrices() {
    dimensions.value.forEach((dimension) => {
        if (!weightMatrix.value[dimension.id]) {
            weightMatrix.value[dimension.id] = new Array(configGroups.value.length).fill(undefined);
        }
        if (!avgScoreMatrix.value[dimension.id]) {
            avgScoreMatrix.value[dimension.id] = new Array(configGroups.value.length).fill(undefined);
        }
        if (!stdDevMatrix.value[dimension.id]) {
            stdDevMatrix.value[dimension.id] = new Array(configGroups.value.length).fill(undefined);
        }
    });
}

// 监听数据变化，向父组件发送更新
watch(
    [configGroups, weightMatrix, avgScoreMatrix, stdDevMatrix],
    () => {
        // 构建符合API数据结构的格式
        const keyPotentialQualityList = configGroups.value.map((group, groupIndex) => ({
            encId: group.id,
            headName: group.headName || '',
            rowDataList: dimensions.value.map((dimension) => ({
                encDimensionId: dimension.id,
                dimensionName: dimension.name,
                weight: weightMatrix.value[dimension.id]?.[groupIndex] || 0,
                normalAverageScore: avgScoreMatrix.value[dimension.id]?.[groupIndex] || 0,
                normalStandardDeviation: stdDevMatrix.value[dimension.id]?.[groupIndex] || 0,
            })),
        }));

        emit('update:modelValue', keyPotentialQualityList);
    },
    { deep: true }
);

// 初始化数据
function initializeData(data: any[]) {
    if (!Array.isArray(data) || data.length === 0) {
        // 初始化默认数据
        configGroups.value = [{ id: '1', headName: '' }];
        dimensions.value = [];
        weightMatrix.value = {};
        avgScoreMatrix.value = {};
        stdDevMatrix.value = {};
        return;
    }

    // 设置配置组
    configGroups.value = data.map((item, index) => ({
        id: item.encId || `${index + 1}`,
        headName: item.headName || '',
    }));

    // 收集所有唯一的维度
    const allDimensions = new Map<string, string>();
    data.forEach((item) => {
        if (item.rowDataList) {
            item.rowDataList.forEach((row: any) => {
                if (row.dimensionName) {
                    allDimensions.set(row.encDimensionId || row.dimensionName, row.dimensionName);
                }
            });
        }
    });

    dimensions.value = Array.from(allDimensions.entries()).map(([id, name]) => ({ id, name }));

    // 初始化矩阵
    weightMatrix.value = {};
    avgScoreMatrix.value = {};
    stdDevMatrix.value = {};

    dimensions.value.forEach((dimension) => {
        weightMatrix.value[dimension.id] = [];
        avgScoreMatrix.value[dimension.id] = [];
        stdDevMatrix.value[dimension.id] = [];

        data.forEach((item) => {
            const rowData = item.rowDataList?.find((row: any) => row.encDimensionId === dimension.id || row.dimensionName === dimension.name);

            weightMatrix.value[dimension.id].push(rowData?.weight);
            avgScoreMatrix.value[dimension.id].push(rowData?.normalAverageScore);
            stdDevMatrix.value[dimension.id].push(rowData?.normalStandardDeviation);
        });
    });
}

// 监听 props 变化
watch(
    () => props.modelValue,
    (newValue) => {
        initializeData(newValue);
    },
    { immediate: true }
);

// 监听维度变化，初始化矩阵
watch(
    () => dimensions.value,
    () => {
        initializeMatrices();
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.key-potential-quality-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.config-groups-header {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 15px 0;
    padding: 15px;
    background: #f7f8fa;
    border-radius: 6px;

    .config-group-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .group-label {
            font-size: 12px;
            color: #666;
            white-space: nowrap;
        }

        .group-name-input {
            width: 200px;
        }
    }
}

.weight-table-container,
.norm-table-container {
    margin: 15px 0;
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e5e6eb;

    th,
    td {
        border: 1px solid #e5e6eb;
        padding: 8px 12px;
        text-align: left;
    }

    th {
        background: #f7f8fa;
        font-weight: 500;
        color: #1d2129;
    }

    .dimension-header {
        width: 150px;
        min-width: 150px;
    }

    .config-header {
        width: 200px;
        min-width: 200px;
        text-align: center;
    }

    .dimension-cell {
        font-weight: 500;
        color: #1d2129;
        background: #fafbfc;
    }

    .weight-cell {
        text-align: center;
    }

    .norm-cell {
        text-align: center;
    }
}

.norm-header {
    text-align: center;

    .norm-header-title {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .norm-header-labels {
        display: flex;
        flex-direction: column;
        gap: 2px;
        font-size: 11px;
        color: #666;

        span {
            display: block;
        }
    }
}

.norm-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .norm-input {
        width: 100%;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}
</style>
