<template>
    <div class="job-quality-model-config">
        <div class="sub-title">
            <h4>岗位素质模型匹配度分数计算</h4>
        </div>

        <!-- 岗位素质模型分数权重表 -->
        <div class="section-title">
            <h5>1、岗位素质模型分数权重表</h5>
            <b-button v-if="configGroups.length < 16" type="outline" size="small" @click="addConfigGroup">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                添加一列
            </b-button>
        </div>

        <div class="weight-table-container">
            <b-table :tableData="tableData" :columns="dynamicColumns" border tableLayoutFixed formMode :scroll="{ y: '300px' }">
                <!-- 二级维度列（固定列） -->
                <template #td-dimensionName="{ raw }">
                    <span class="dimension-name">{{ raw.dimensionName }}</span>
                </template>

                <!-- 动态权重列 -->
                <template v-for="(group, groupIndex) in configGroups" :key="`weight_${groupIndex}`" #[`td-weight_${groupIndex}`]="{ raw }">
                    <b-form-item :field="`weight_${groupIndex}_${raw.dimensionId}`" noStyle>
                        <b-input-number v-model="raw.weights[groupIndex]" placeholder="权重" hideButton :min="0" :max="1" :precision="4" @change="validateWeightSum(groupIndex)" />
                    </b-form-item>
                </template>
            </b-table>

            <!-- 权重和验证错误提示 -->
            <div v-for="(error, index) in weightSumErrors" :key="index" class="error-tip">
                {{ error }}
            </div>
        </div>

        <!-- 岗位素质模型常模 -->
        <div class="section-title">
            <h5>2、岗位素质模型常模</h5>
        </div>

        <div class="norm-table-container">
            <b-table :tableData="normTableData" :columns="dynamicNormColumns" border tableLayoutFixed formMode :scroll="{ y: '300px' }">
                <!-- 二级维度列（固定列） -->
                <template #td-dimensionName="{ raw }">
                    <span class="dimension-name">{{ raw.dimensionName }}</span>
                </template>

                <!-- 动态常模列 -->
                <template v-for="(group, groupIndex) in configGroups" :key="`norm_${groupIndex}`" #[`td-norm_${groupIndex}`]="{ raw }">
                    <div class="norm-cell">
                        <b-form-item :field="`avgScore_${groupIndex}_${raw.dimensionId}`" noStyle class="norm-input">
                            <b-input-number v-model="raw.avgScores[groupIndex]" placeholder="常模平均分" hideButton :min="0" :max="1000" :precision="4" />
                        </b-form-item>
                        <b-form-item :field="`stdDev_${groupIndex}_${raw.dimensionId}`" noStyle class="norm-input">
                            <b-input-number v-model="raw.stdDevs[groupIndex]" placeholder="常模标准差" hideButton :min="0.0001" :max="1000" :precision="4" />
                        </b-form-item>
                    </div>
                </template>
            </b-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';

interface ConfigGroup {
    id: string;
    headName: string;
}

interface DimensionRow {
    dimensionId: string;
    dimensionName: string;
    weights: (number | undefined)[];
    avgScores: (number | undefined)[];
    stdDevs: (number | undefined)[];
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

// 配置组数据（对应后端的每个headName组）
const configGroups = ref<ConfigGroup[]>([{ id: '1', headName: '' }]);

// 维度数据（从后端数据中提取的所有唯一维度）
const dimensions = ref<{ id: string; name: string }[]>([]);

// 表格数据
const tableData = ref<DimensionRow[]>([]);
const normTableData = ref<DimensionRow[]>([]);

// 动态生成权重表列配置
const dynamicColumns = computed(() => {
    const columns = [
        {
            label: '二级维度',
            field: 'dimensionName',
            width: 150,
            fixed: 'left',
        },
    ];

    // 为每个配置组添加一列
    configGroups.value.forEach((group, index) => {
        columns.push({
            label: group.headName || `配置${index + 1}`,
            field: `weight_${index}`,
            width: 200,
            headerRender: () => createHeaderWithEdit(group, index),
        });
    });

    return columns;
});

// 动态生成常模表列配置
const dynamicNormColumns = computed(() => {
    const columns = [
        {
            label: '二级维度',
            field: 'dimensionName',
            width: 150,
            fixed: 'left',
        },
    ];

    // 为每个配置组添加常模列
    configGroups.value.forEach((group, index) => {
        columns.push({
            label: group.headName || `配置${index + 1}`,
            field: `norm_${index}`,
            width: 250,
            headerRender: () => createNormHeader(group.headName || `配置${index + 1}`),
        });
    });

    return columns;
});

// 创建可编辑的表头
function createHeaderWithEdit(group: ConfigGroup, index: number) {
    return h('div', { class: 'editable-header' }, [
        h('input', {
            value: group.headName,
            placeholder: '请输入名称',
            class: 'header-input',
            onInput: (e: Event) => {
                group.headName = (e.target as HTMLInputElement).value;
            },
        }),
        h(
            'button',
            {
                class: 'delete-btn',
                onClick: () => removeConfigGroup(index),
            },
            '删除'
        ),
    ]);
}

// 创建常模表头
function createNormHeader(title: string) {
    return h('div', { class: 'norm-header' }, [
        h('div', { class: 'norm-header-title' }, title),
        h('div', { class: 'norm-header-labels' }, [h('span', '常模平均分'), h('span', '常模标准差')]),
    ]);
}

// 权重和验证错误
const weightSumErrors = ref<string[]>([]);

// 验证指定配置组的权重和
function validateWeightSum(groupIndex: number) {
    const sum = tableData.value.reduce((acc, row) => acc + (row.weights[groupIndex] || 0), 0);
    const groupName = configGroups.value[groupIndex]?.headName || `配置${groupIndex + 1}`;

    if (Math.abs(sum - 1) > 0.0001) {
        weightSumErrors.value[groupIndex] = `${groupName} 权重和必须等于 1，当前为 ${sum.toFixed(4)}`;
    } else {
        weightSumErrors.value[groupIndex] = '';
    }

    // 清理空的错误信息
    weightSumErrors.value = weightSumErrors.value.filter((error) => error !== '');
}

// 添加配置组（列）
function addConfigGroup() {
    if (configGroups.value.length >= 16) return;

    const newId = Date.now().toString();
    configGroups.value.push({
        id: newId,
        headName: '',
    });

    // 为所有维度添加新的权重和常模数据
    tableData.value.forEach((row) => {
        row.weights.push(undefined);
        row.avgScores.push(undefined);
        row.stdDevs.push(undefined);
    });

    normTableData.value.forEach((row) => {
        row.weights.push(undefined);
        row.avgScores.push(undefined);
        row.stdDevs.push(undefined);
    });
}

// 删除配置组（列）
function removeConfigGroup(index: number) {
    if (configGroups.value.length <= 1) return;

    configGroups.value.splice(index, 1);

    // 从所有维度中删除对应的权重和常模数据
    tableData.value.forEach((row) => {
        row.weights.splice(index, 1);
        row.avgScores.splice(index, 1);
        row.stdDevs.splice(index, 1);
    });

    normTableData.value.forEach((row) => {
        row.weights.splice(index, 1);
        row.avgScores.splice(index, 1);
        row.stdDevs.splice(index, 1);
    });

    // 重新验证所有配置组的权重和
    configGroups.value.forEach((_, groupIndex) => {
        validateWeightSum(groupIndex);
    });
}

// 同步表格数据到常模表格
watch(
    () => tableData.value,
    (newTableData) => {
        normTableData.value = newTableData.map((row) => ({ ...row }));
    },
    { deep: true }
);

// 监听数据变化，向父组件发送更新
watch(
    [configGroups, tableData],
    () => {
        // 构建符合API数据结构的格式
        const positionQualityModelMatchList = configGroups.value.map((group, groupIndex) => ({
            encId: group.id,
            headName: group.headName || '',
            rowDataList: tableData.value.map((row) => ({
                encDimensionId: row.dimensionId,
                dimensionName: row.dimensionName,
                weight: row.weights[groupIndex] || 0,
                normalAverageScore: row.avgScores[groupIndex] || 0,
                normalStandardDeviation: row.stdDevs[groupIndex] || 0,
            })),
        }));

        emit('update:modelValue', positionQualityModelMatchList);
    },
    { deep: true }
);

// 初始化数据
function initializeData(data: any[]) {
    if (!Array.isArray(data) || data.length === 0) {
        // 初始化默认数据
        configGroups.value = [{ id: '1', headName: '' }];
        dimensions.value = [];
        tableData.value = [];
        normTableData.value = [];
        return;
    }

    // 设置配置组
    configGroups.value = data.map((item, index) => ({
        id: item.encId || `${index + 1}`,
        headName: item.headName || '',
    }));

    // 收集所有唯一的维度
    const allDimensions = new Map<string, string>();
    data.forEach((item) => {
        if (item.rowDataList) {
            item.rowDataList.forEach((row: any) => {
                if (row.dimensionName) {
                    allDimensions.set(row.encDimensionId || row.dimensionName, row.dimensionName);
                }
            });
        }
    });

    dimensions.value = Array.from(allDimensions.entries()).map(([id, name]) => ({ id, name }));

    // 构建表格数据
    tableData.value = dimensions.value.map((dimension) => {
        const weights: (number | undefined)[] = [];
        const avgScores: (number | undefined)[] = [];
        const stdDevs: (number | undefined)[] = [];

        data.forEach((item) => {
            const rowData = item.rowDataList?.find((row: any) => row.encDimensionId === dimension.id || row.dimensionName === dimension.name);

            weights.push(rowData?.weight);
            avgScores.push(rowData?.normalAverageScore);
            stdDevs.push(rowData?.normalStandardDeviation);
        });

        return {
            dimensionId: dimension.id,
            dimensionName: dimension.name,
            weights,
            avgScores,
            stdDevs,
        };
    });

    // 同步常模表格数据
    normTableData.value = tableData.value.map((row) => ({ ...row }));
}

// 监听 props 变化
watch(
    () => props.modelValue,
    (newValue) => {
        initializeData(newValue);
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.job-quality-model-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.weight-table-container,
.norm-table-container {
    margin: 15px 0;
}

.dimension-name {
    font-weight: 500;
    color: #1d2129;
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.editable-header {
    display: flex;
    align-items: center;
    gap: 8px;

    .header-input {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 12px;

        &:focus {
            border-color: #1890ff;
            outline: none;
        }
    }

    .delete-btn {
        padding: 2px 6px;
        background: #ff4d4f;
        color: white;
        border: none;
        border-radius: 3px;
        font-size: 11px;
        cursor: pointer;

        &:hover {
            background: #ff7875;
        }
    }
}

.norm-header {
    text-align: center;

    .norm-header-title {
        font-weight: 500;
        margin-bottom: 4px;
    }

    .norm-header-labels {
        display: flex;
        flex-direction: column;
        gap: 2px;
        font-size: 11px;
        color: #666;

        span {
            display: block;
        }
    }
}

.norm-cell {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .norm-input {
        margin: 0;
    }
}
</style>
