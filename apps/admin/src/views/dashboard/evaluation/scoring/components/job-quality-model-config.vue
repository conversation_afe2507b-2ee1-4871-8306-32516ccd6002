<template>
    <div class="job-quality-model-config">
        <div class="sub-title">
            <h4>岗位素质模型匹配度分数计算</h4>
        </div>

        <!-- 岗位素质模型分数权重表 -->
        <div class="section-title">
            <h5>1、岗位素质模型分数权重表</h5>
            <b-button v-if="weightList.length < 16" type="outline" size="small" @click="addWeightItem">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                增加岗位素质模型
            </b-button>
        </div>

        <!-- 岗位素质模型名称 -->
        <div class="head-name-section">
            <b-form-item label="岗位素质模型名称" field="headName" asteriskPosition="end">
                <b-input v-model="headName" placeholder="请输入岗位素质模型名称" />
            </b-form-item>
        </div>

        <b-table v-model:tableData="weightList" :columns="weightColumns" border tableLayoutFixed formMode :scroll="{ y: '300px' }">
            <template #td-name="{ raw, $index }">
                <b-form-item :field="`name_${$index}`" noStyle>
                    <b-input v-model="raw.name" placeholder="请输入岗位素质模型名称" />
                </b-form-item>
            </template>

            <template #td-weight="{ raw, $index }">
                <b-form-item :field="`weight_${$index}`" noStyle>
                    <b-input-number v-model="raw.weight" placeholder="请输入权重" hideButton :min="0" :max="1" :precision="4" @change="validateWeightSum" />
                </b-form-item>
            </template>

            <template #td-operation="{ $index }">
                <b-action @click="removeWeightItem($index)">删除</b-action>
            </template>
        </b-table>

        <div v-if="weightSumError" class="error-tip">权重和必须等于 1</div>

        <!-- 岗位素质模型常模 -->
        <div class="section-title">
            <h5>2、岗位素质模型常模</h5>
        </div>

        <b-table v-model:tableData="normList" :columns="normColumns" border tableLayoutFixed formMode :scroll="{ y: '300px' }">
            <template #td-name="{ raw }">
                {{ raw.name }}
            </template>

            <template #td-avgScore="{ raw, $index }">
                <b-form-item :field="`avgScore_${$index}`" noStyle>
                    <b-input-number v-model="raw.avgScore" placeholder="请填写0-1000内数值，最多4位小数" hideButton :min="0" :max="1000" :precision="4" />
                </b-form-item>
            </template>

            <template #td-stdDev="{ raw, $index }">
                <b-form-item :field="`stdDev_${$index}`" noStyle>
                    <b-input-number v-model="raw.stdDev" placeholder="请填写1000以内正数，最多4位小数" hideButton :min="0.0001" :max="1000" :precision="4" />
                </b-form-item>
            </template>
        </b-table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface WeightItem {
    id: string;
    name: string;
    weight: number | undefined;
}

interface NormItem {
    id: string;
    name: string;
    avgScore: number | undefined;
    stdDev: number | undefined;
}

const props = defineProps<{
    modelValue: any;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: any];
}>();

// 岗位素质模型名称
const headName = ref<string>('');

// 权重表数据
const weightList = ref<WeightItem[]>([{ id: '1', name: '', weight: undefined }]);

// 常模数据
const normList = ref<NormItem[]>([]);

// 权重表列配置
const weightColumns = [
    {
        label: '岗位素质模型名称',
        field: 'name',
        width: 200,
    },
    {
        label: '权重',
        field: 'weight',
        width: 150,
    },
    {
        label: '操作',
        field: 'operation',
        width: 100,
    },
];

// 常模表列配置
const normColumns = [
    {
        label: '岗位素质模型名称',
        field: 'name',
        width: 200,
    },
    {
        label: '常模平均分',
        field: 'avgScore',
        width: 150,
    },
    {
        label: '常模标准差',
        field: 'stdDev',
        width: 150,
    },
];

// 权重和验证
const weightSumError = ref(false);

function validateWeightSum() {
    const sum = weightList.value.reduce((acc, item) => acc + (item.weight || 0), 0);
    weightSumError.value = Math.abs(sum - 1) > 0.0001;
}

// 添加权重项
function addWeightItem() {
    if (weightList.value.length >= 16) return;

    const newId = Date.now().toString();
    weightList.value.push({
        id: newId,
        name: '',
        weight: undefined,
    });

    // 同步添加常模项
    normList.value.push({
        id: newId,
        name: '',
        avgScore: undefined,
        stdDev: undefined,
    });
}

// 删除权重项
function removeWeightItem(index: number) {
    if (weightList.value.length <= 1) return;

    weightList.value.splice(index, 1);
    normList.value.splice(index, 1);
    validateWeightSum();
}

// 监听权重表名称变化，同步到常模表
watch(
    () => weightList.value.map((item) => ({ id: item.id, name: item.name })),
    (newNames) => {
        newNames.forEach((item, index) => {
            if (normList.value[index]) {
                normList.value[index].name = item.name;
            }
        });
    },
    { deep: true }
);

// 监听数据变化，向父组件发送更新
watch(
    [headName, weightList, normList],
    () => {
        // 构建符合API数据结构的格式
        const positionQualityModelMatchList = [{
            encId: '', // 可以根据需要设置
            headName: headName.value || '',
            rowDataList: weightList.value.map((item, index) => ({
                encDimensionId: item.id || '',
                dimensionName: item.name || '',
                weight: item.weight || 0,
                normalAverageScore: normList.value[index]?.avgScore || 0,
                normalStandardDeviation: normList.value[index]?.stdDev || 0,
            }))
        }];

        emit('update:modelValue', positionQualityModelMatchList);
    },
    { deep: true }
);

// 初始化数据
watch(
    () => props.modelValue,
    (newValue) => {
        if (Array.isArray(newValue) && newValue.length > 0) {
            const firstItem = newValue[0];

            // 设置headName
            headName.value = firstItem?.headName || '';

            if (firstItem?.rowDataList) {
                weightList.value = firstItem.rowDataList.map((row: any) => ({
                    id: row.encDimensionId || Date.now().toString(),
                    name: row.dimensionName || '',
                    weight: row.weight || 0,
                }));

                normList.value = firstItem.rowDataList.map((row: any) => ({
                    id: row.encDimensionId || Date.now().toString(),
                    name: row.dimensionName || '',
                    avgScore: row.normalAverageScore || 0,
                    stdDev: row.normalStandardDeviation || 0,
                }));
            }
        } else if (newValue?.weightList) {
            // 兼容旧格式
            weightList.value = newValue.weightList;
        } else if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
            // 初始化默认数据
            if (weightList.value.length === 0) {
                addWeightItem();
            }
        }
        if (newValue?.normList) {
            // 兼容旧格式
            normList.value = newValue.normList;
        }
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
.job-quality-model-config {
    margin: 20px 0;
}

.sub-title {
    margin: 20px 0 10px 0;

    h4 {
        font-size: 14px;
        font-weight: 600;
        color: #1d2129;
        margin: 0;
    }
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;

    h5 {
        font-size: 13px;
        font-weight: 500;
        color: #1d2129;
        margin: 0;
    }
}

.error-tip {
    color: #f53f3f;
    font-size: 12px;
    margin-top: 5px;
}

.head-name-section {
    margin: 10px 0 15px 0;
}
</style>
